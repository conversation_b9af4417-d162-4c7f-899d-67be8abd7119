// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyChaseState : EnemyState
    {
        private float _timer;
        private bool _isGettingBored;

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);
            _timer = 0f;
            _isGettingBored = false;
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (_spiderController.DistanceToTarget <= _spiderController.AttackDistance)
            {
                _spiderController.ChangeState(SpiderController.EnemyEnum.Attack);
            }

            if (_spiderController.DistanceToTarget >= _spiderController.LoseSightDistance)
            {
                _spiderController.ChangeState(SpiderController.EnemyEnum.Patrol);
                _isGettingBored = true;
            }

            if (_isGettingBored)
            {
                _timer += Time.deltaTime;
                if (_timer >= _spiderController.BoredDuration)
                {
                    _spiderController.ChangeState(SpiderController.EnemyEnum.Retreat);
                }
            }

            // stay in Chase
            return this;
        }

        public override void Exit(MonoStateMachine controller)
        {

        }
    }
}