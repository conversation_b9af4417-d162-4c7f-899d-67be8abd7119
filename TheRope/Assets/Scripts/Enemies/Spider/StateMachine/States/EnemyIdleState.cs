// Copyright Isto Inc.


using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyIdleState : EnemyState
    {
        // UNITY HOOKUP



        // OTHER FIELDS

        private float _timer;

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);
            _timer = 0f;
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            _timer += Time.deltaTime;

            if(!Mathf.Approximately(Mathf.Floor(_timer), Mathf.Floor(_timer - Time.deltaTime)))
            {
                Debug.Log("Spider is Idling...");
            }

            // Example transition: after idleDuration, go patrol
            if (_timer >= _spiderController.IdleDuration)
                _spiderController.ChangeState(SpiderController.EnemyEnum.Patrol);

            if (_spiderController.TryFindNearestPlayerWithinRadius())
            {
                _spiderController.ChangeState(SpiderController.EnemyEnum.Chase);
            }

            return this;
        }

        public override void Exit(MonoStateMachine controller)
        {
            // cleanup if needed
        }
    }
}